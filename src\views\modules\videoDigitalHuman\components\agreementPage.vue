<template>
    <el-dialog v-model="isShow" width="420">
        <template #title>
            <div class="title">使用者承诺须知</div>
        </template>
        <div class="info">
            当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗当前克隆的音色未保存，返回后需重新生成，确认要返回吗
        </div>
        <div class="footer">
            <div class="confirm" :class="{ active: num == 0 }" @click="confirm('confirm')">我已知晓，同意（{{ num }}s）</div>
            <div class="cancel" @click="confirm('cancel')">取消</div>
        </div>
    </el-dialog>
</template>

<script setup>
import { computed, ref, onMounted, defineEmits } from 'vue';
const props = defineProps({ dialogAgreementVisible: { type: Boolean, default: false } })
const emit = defineEmits(['update'])

const isShow = computed(() => { return props.dialogAgreementVisible })
const num = ref(3)

let timer = null
onMounted(() => {
    timer = setInterval(() => {
        num.value--
        if (num.value == 0) {
            clearInterval(timer)
            timer = null
        }
    }, 1000)
})

const confirm = (text) => {
    if ((text == 'confirm' && num.value == 0) || text == 'cancel') {
        emit('update', false)
    }
}

</script>

<style scoped lang="scss">
* {
    box-sizing: border-box;
}

.title {
    font-size: 16px;
    color: #000000;
    font-weight: 500;
}

.info {
    font-size: 12px;
    color: #1E1E1E;
    line-height: 2;
    letter-spacing: 1px;
}

.footer {
    margin: 40px 0 0 0;

    div {
        height: 40px;
        border-radius: 4px;
        text-align: center;
        line-height: 40px;
        color: #fff;
        font-size: 14px;
        font-weight: 500;
        float: right;
    }

    .cancel {
        width: 96px;
        background: #D3D3D2;
        margin-right: 10px;
        cursor: pointer;
    }

    .confirm {
        width: 163px;
        background: rgba(10, 175, 96, 0.75);
    }

    .active {
        background: #0AAF60;
        cursor: pointer;
    }
}
</style>
<style lang="scss">
.el-dialog__headerbtn .el-dialog__close {
    display: none;
}
</style>